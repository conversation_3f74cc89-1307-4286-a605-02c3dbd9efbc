# Multi-stage build para api-worker
FROM node:lts-alpine AS deps

RUN npm install -g pnpm@10.15.1

WORKDIR /app

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/api-worker/package.json ./apps/api-worker/
COPY shared/package.json ./shared/

# Install all dependencies
RUN pnpm install --frozen-lockfile

# Build stage
FROM deps AS builder

# Copy source code
COPY . .

# Build shared y api-worker
RUN pnpm build:shared
RUN pnpm --filter api-worker build

# Production stage
FROM node:lts-alpine AS runner

# Install security updates and required packages
RUN apk update && \
  apk upgrade && \
  apk add --no-cache \
  curl \
  ca-certificates \
  gnupg && \
  rm -rf /var/cache/apk/*

# Install Doppler CLI
RUN curl -Ls --tlsv1.2 --proto "=https" --retry 3 \
  https://cli.doppler.com/install.sh | sh

RUN npm install -g pnpm@10.15.1

WORKDIR /app

# Set production environment
ENV NODE_ENV=production

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 --ingroup nodejs apiworker

# Copy built app
COPY --from=builder --chown=apiworker:nodejs /app/shared/dist ./shared/dist
COPY --from=builder --chown=apiworker:nodejs /app/apps/api-worker/dist ./apps/api-worker/dist

COPY --from=builder /app/service.manifest.json ./service.manifest.json

COPY apps/api-worker/tsconfig-paths.json /app/apps/api-worker/tsconfig-paths.json

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/api-worker/package.json ./apps/api-worker/
COPY shared/package.json ./shared/

# Install production dependencies
RUN pnpm install --frozen-lockfile --prod --ignore-scripts

# Switch to non-root user
USER apiworker

# Set environment variable para tsconfig-paths
ENV TS_NODE_PROJECT=tsconfig-paths.json

# Start application
WORKDIR /app/apps/api-worker
CMD ["doppler", "run", "--", "node", "-r", "tsconfig-paths/register", "dist/main.js"]
