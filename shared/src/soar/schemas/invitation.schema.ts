import { Document, Types, Schema } from 'mongoose';
import crypto from 'crypto';

import modelFactory from '@shared/utils/model-factory';

import { InvitationModel, INVITATION_STATUS } from '../models/invitation.model';

import { ROLES } from '../helpers/constants';

const invitationSchema = new Schema(
  {
    email: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    role: { type: String, enum: ROLES, required: true },
    status: { type: String, enum: INVITATION_STATUS, default: 'pending' },
    token: { type: String, required: true, unique: true },
    expiresAt: { type: Date, required: true },
    revokedBy: { type: String, default: null },
    revokedAt: { type: Date, default: null },
    acceptedAt: { type: Date, default: null },
    createdBy: { type: String, required: true },
    updatedBy: { type: String, required: true },
    deleted: { type: Boolean, default: false },
    enabled: { type: Boolean, default: true },
    protected: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  }
);

invitationSchema.index({ email: 1 }, { unique: true });
invitationSchema.index({ token: 1 }, { unique: true });
invitationSchema.index({ status: 1 });
invitationSchema.index({ deleted: 1 });
invitationSchema.index({ enabled: 1 });
invitationSchema.index(
  { updatedAt: 1 },
  { expireAfterSeconds: 45 * 24 * 60 * 60 } // 45 días
);

invitationSchema.statics.generateToken = async function (bytes = 32, maxAttempts = 10) {
  for (let i = 0; i < maxAttempts; i++) {
    // 256-bit random -> ~43 chars URL-safe
    const token = crypto.randomBytes(bytes);
    const exists = await this.exists({ token });
    if (!exists) return token.toString('hex');
  }
};

export type InvitationDocument = InvitationModel & Document & { _id: Types.ObjectId };
export const Invitation = modelFactory<InvitationModel>('Invitation', invitationSchema);
