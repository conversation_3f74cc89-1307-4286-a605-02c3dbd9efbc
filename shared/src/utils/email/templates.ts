import { TLanguage } from '@shared/types/languages.types';
import { CLIENT_NAME } from '@shared/constants/env';

type BaseTemplateValues = {
  language?: TLanguage;
  subject?: string;
};

/**
 * Template for request error.
 *
 * The expected values are:
 * - language
 * - subject
 * - data
 *
 */
const requestError = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <p class="m-0 font-medium text-xl">
      Unexpected Error for ${CLIENT_NAME}
    </p>
    <div class="leading-24">&nbsp;</div>

    <p class="m-0">
      An unexpected error occurred. See details below:
    </p>
    <div class="leading-24">&nbsp;</div>

    <each loop="value, key in data">
      <p class="m-0 mb-8">
        <span class="font-semibold">{{ key }}</span>: <code>{{ value }}</code>
      </p>
    </each>
  </block>
</extends>`;

export type GenericDataValues = {
  data: { [key: string]: any };
} & BaseTemplateValues;

/**
 * Template for temps full error.
 *
 * The expected values are:
 * - language
 * - subject
 *
 */
const tempsFullError = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <p class="m-0 font-medium text-xl">
      There are too many temps for ${CLIENT_NAME}
    </p>
    <div class="leading-24">&nbsp;</div>

    <p class="m-0">
      It was not possible to generate a new temp session, please review the state of the collection in the DB.
    </p>
  </block>
</extends>`;

/**
 * Template for bot error.
 *
 * The expected values are:
 * - language
 * - subject
 * - data
 *
 */
const botError = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <p class="m-0 font-medium text-xl">
      Telegram Bot Error for ${CLIENT_NAME}
    </p>
    <div class="leading-24">&nbsp;</div>

    <p class="m-0">
      There was an error with the Telegram Bot. See details below:
    </p>
    <div class="leading-24">&nbsp;</div>

    <each loop="value, key in data">
      <p class="m-0 mb-8">
        <span class="font-semibold">{{ key }}</span>: <code>{{ value }}</code>
      </p>
    </each>
  </block>
</extends>`;

/**
 * Template for authentication code.
 *
 * The expected values are:
 * - language
 * - subject
 * - title
 * - platform
 * - code
 *
 */
const authCode = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <p class="m-0">
      {{title}} <strong>{{platform}}</strong>:
    </p>
    <div class="leading-24">&nbsp;</div>

    <p class="m-0 font-bold text-2xl uppercase text-center tracking-widest">{{code}}</p>
    <div class="leading-24">&nbsp;</div>
  </block>
</extends>`;

export type AuthCodeValues = {
  title: string;
  platform: string;
  code: string;
} & BaseTemplateValues;

/**
 * Template for authentication code.
 *
 * The expected values are:
 * startDate
 * endDate
 * newEvents
 * blockedEvents
 * allowedEvents
 * criticalEvents
 * totalDevices
 * newDevices
 * disconnectedDevices
 * mostAlerted
 * mostCritical
 *
 */
const halcyonReport = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <div>
        <table class="w-full border-0">
            <tr>
                <td class="align-middle text-left">
                    <p class="inline-block w-2/4 m-0 text-left text-2xl font-bold">Monthly report</p>
                </td>
                <td class="align-middle text-right">
                    <p class="inline-block text-right m-0 text-gray-500 text-s ">{{startDate}} - {{endDate}}</p>
                </td>
            </tr>
        </table>
        <p class="mt-3 text-justify">Dear customer, the report corresponding to the detections collected and devices information seen during the month is attached.</p>
    </div>
    <div class="mt-5">
        <p class="m-0 text-left text-xl font-bold">Eventos</p>
        <p class="mt-3 text-justify">This section presents the information on events collected in the period.</p>
    </div>
    <div class="rounde">
        <p class="m-0 text-left text-lg font-bold">Events Summary</p>
        <div class="mt-3">
            <table class="w-full border-0">
                <tr>
                    <td class="align-middle text-left"><p class="m-0">Registered</p></td>
                    <td class="align-middle text-right"><p class="m-0">{{newEvents}}</p></td>
                </tr>
            </table>
            <table class="w-full border-0">
                <tr>
                    <td class="align-middle text-left"><p class="m-0">Blocked</p></td>
                    <td class="align-middle text-right"><p class="m-0">{{blockedEvents}}</p></td>
                </tr>
            </table>
            <table class="w-full border-0">
                <tr>
                    <td class="align-middle text-left"><p class="m-0">Allowed</p></td>
                    <td class="align-middle text-right"><p class="m-0">{{allowedEvents}}</p></td>
                </tr>
            </table>
            <table class="w-full border-0">
                <tr>
                    <td class="align-middle text-left"><p class="m-0">Critical</p></td>
                    <td class="align-middle text-right"><p class="m-0">{{criticalEvents}}</p></td>
                </tr>
            </table>
        </div>
    </div>
    <div class="mt-5">
        <p class="m-0 text-left text-xl font-bold">Devices</p>
        <p class="mt-3 text-justify">This section presents the activity of the devices seen in the period.</p>
    </div>
    <div class="rounde">
        <p class="m-0 text-left text-lg font-bold">Device Summary</p>
        <div class="mt-3">
            <table class="w-full border-0">
                <tr>
                    <td class="align-middle text-left"><p class="m-0">Total</p></td>
                    <td class="align-middle text-right"><p class="m-0">{{totalDevices}}</p></td>
                </tr>
            </table>
            <table class="w-full border-0">
                <tr>
                    <td class="align-middle text-left"><p class="m-0">New Devices</p></td>
                    <td class="align-middle text-right"><p class="m-0">{{newDevices}}</p></td>
                </tr>
            </table>
            <table class="w-full border-0">
                <tr>
                    <td class="align-middle text-left"><p class="m-0">Inactive</p></td>
                    <td class="align-middle text-right"><p class="m-0">{{disconnectedDevices}}</p></td>
                </tr>
            </table>
        </div>
    </div>
    <div class="rounde mt-5">
        <p class="m-0 text-left text-lg font-bold">Devices with more alerts</p>
        <div class="mt-3">
            <each loop="value in mostAlerted">
                <table class="w-full border-0">
                    <tr>
                        <td class="align-middle text-left"><p class="m-0">{{value.name}}</p></td>
                        <td class="align-middle text-right"><p class="m-0">{{value.count}}</p></td>
                    </tr>
                </table>
            </each>
        </div>
    </div>
    <div class="rounde mt-5">
        <p class="m-0 text-left text-lg font-bold">Most critical devices</p>
        <div class="mt-3" >
            <each loop="value in mostCritical">
                <table class="w-full border-0">
                    <tr>
                        <td class="align-middle text-left"><p class="m-0">{{value.name}}</p></td>
                        <td class="align-middle text-right"><p class="m-0">{{value.count}}</p></td>
                    </tr>
                </table>
            </each>
        </div>
    </div>
  </block>
</extends>`;

export type HalcyonReportValues = {
  startDate: string;
  endDate: string;
  newEvents: number;
  blockedEvents: number;
  allowedEvents: number;
  criticalEvents: number;
  totalDevices: number;
  newDevices: number;
  disconnectedDevices: number;
  mostAlerted: { name: string; count: number }[];
  mostCritical: { name: string; count: number }[];
} & BaseTemplateValues;

/**
 * Template for authentication code.
 *
 * The expected values are:
 * - title
 * - content
 * - policyName
 * - userName
 * - hostsData
 *
 */
const applicationPolicy = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <p class="m-0 text-xl font-medium">{{title}}</p>

    <div class="leading-8">&nbsp;</div>

    <p class="m-0 text-gray-25">{{subtitle}}.</p>
    <p class="m-0 text-gray-25">"{{policyName}}"</p>

    <div class="leading-16">&nbsp;</div>

    <table class="w-full">
      <tr>
        <td class="w-12/12 w-full">
          <div class="number-card">
            <p class="m-0 font-medium">{{targeted_hosts}}</p>
            <p class="m-8 mt-12 text-5xl">{{targeted_hosts_value}}</p>
          </div>
        </td>
      </tr>
    </table>

    <div class="leading-8">&nbsp;</div>

    <table class="w-full">
      <tr>
        <td class="w-4/12">
          <div class="number-card">
            <p class="m-0 font-medium text-success-600">{{compliant}}</p>
            <p class="m-8 mt-12 text-5xl">{{compliant_value}}</p>
          </div>
        </td>
        <td class="w-4/12 px-16">
          <div class="number-card">
            <p class="m-0 font-medium text-danger-700">{{non_compliant}}</p>
            <p class="m-8 mt-12 text-5xl">{{non_compliant_value}}</p>
          </div>
        </td>
        <td class="w-4/12">
          <div class="number-card">
            <p class="m-0 font-medium text-warning-400">{{no_inventory}}</p>
            <p class="m-8 mt-12 text-5xl">{{no_inventory_value}}</p>
          </div>
        </td>
      </tr>
    </table>

    <div class="leading-16">&nbsp;</div>

    <table class="w-full">
      <tr>
        <td class="w-4/12 sm:w-0"></td>
        <td class="w-4/12 sm:w-10/12 bg-blue-light rounded-xl" style="mso-padding-alt: 16px 24px">
          <a
          href="{{buttonURL}}"
          class="block font-light text-black text-2xl text-center uppercase leading-normal py-8 px-16 no-underline tracking-wider"
          >{{buttonText}}</a
          >
          <td class="w-4/12 sm:w-0"></td>
      </tr>
    </table>
    
    <div class="leading-24">&nbsp;</div>
    <div class="text-s text-gray-25">
      <p class="m-0">{{buttonAlt}}:</p>
      <p class="m-0 mt-8 leading-16">
        <a href="{{buttonURL}}" class="text-blue-light">
          {{buttonURL}}
        </a>
      </p>
    </div>
    
    <div class="leading-24">&nbsp;</div>

    <p class="m-0 text-gray-25">
      {{contact_desc}}
      <a href="mailto:<EMAIL>" class="text-blue-light">
        {{contact}}
      </a>
    </p>

    <div class="leading-24">&nbsp;</div>

    <p class="m-0 text-gray-25">{{regards}}</p>
    <p class="m-0 text-xl font-medium">{{batuta}}</p>
  </block>
</extends>`;

export type ApplicationPolicyValues = {
  greeting: string;
  title: string;
  subtitle: string;
  policyName: string;
  buttonURL: string;
  buttonText: string;
  buttonAlt: string;
  targeted_hosts: string;
  targeted_hosts_value: number;
  compliant: string;
  compliant_value: number;
  non_compliant: string;
  non_compliant_value: number;
  no_inventory: string;
  no_inventory_value: number;
  contact_desc: string;
  contact: string;
  regards: string;
  batuta: string;
} & BaseTemplateValues;

/**
 * Template for Batuta Report.
 *
 * The expected values are:
 * - title
 * - greeting
 * - description
 * - farewell
 * - team
 *
 */
const batutaReport = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <p class="m-0 font-medium text-xl">{{title}}</p>
    <div class="leading-24">&nbsp;</div>

    <p class="m-0">{{greeting}}</p>
    <div class="leading-24">&nbsp;</div>
    <p class="m-0">{{description}}</p>
    <div class="leading-24">&nbsp;</div>
    <p class="m-0">{{farewell}}</p>
    <p class="m-0">{{team}}</p>
  </block>
</extends>`;

export type BatutaReportValues = {
  title: string;
  greeting: string;
  description: string;
  farewell: string;
  team: string;
} & BaseTemplateValues;

const awarenessAssignation = `
<extends src="@shared/src/utils/email/layout.html">
  <block name="template">
    <p class="m-0 font-medium text-xl">A new awareness training has been assigned to your account: {{title}}</p>
    <div class="leading-24">&nbsp;</div>

    <component src="../.@shared/src/utils/email/components/button.html" locals='{"buttonURL": "{{code}}", "buttonAlt": "Take the training now", "buttonText": "Click here to begin the training"}'></component>
    <div class="leading-24">&nbsp;</div>
      </block>
</extends>`;

export type AwarenessAssignationValues = {
  title: string;
  code: string;
} & BaseTemplateValues;

/**
 * Template for User Invitation.
 *
 * The expected values are *
 * - invitedUser
 * - adminName
 * - title
 * - body1
 * - linkText
 * - linkURL
 * - linkNotWorking
 * - warnin1
 * - warning2
 */

const newInvitation = `
<extends src="src/layouts/main.html">
  <block name="template">
    <p class="m-0 font-medium text-xl">{{page.title}} {{page.invitedUser}},</p>
    <div class="leading-24">&nbsp;</div>

    <p class="m-0">{{page.body1}} {{page.adminName}}</p>
    <div class="leading-24">&nbsp;</div>

    <component
      src="src/components/button.html"
      locals='{"buttonURL": "{{page.linkURL}}", "buttonText": "{{page.linkText}}", "buttonAlt": "{{page.linkNotWorking}}"}'
    ></component>

    <div class="leading-24">&nbsp;</div>

    <p class="m-0">{{page.warnin1}}</p>

    <div class="leading-8">&nbsp;</div>

    <p class="m-0">{{page.warning2}}</p>
  </block>
</extends>`;

export type NewInvitationValues = {
  invitedUser: string;
  adminName: string;
  title: string;
  body1: string;
  linkText: string;
  linkURL: string;
  linkNotWorking: string;
  warnin1: string;
  warning2: string;
} & BaseTemplateValues;

export default {
  requestError,
  botError,
  authCode,
  halcyonReport,
  tempsFullError,
  applicationPolicy,
  awarenessAssignation,
  batutaReport,
  newInvitation,
};
