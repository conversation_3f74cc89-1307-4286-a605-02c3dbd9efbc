import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '../../../utils/model-factory';

import { ApplicationPolicyStatusModel } from '../helpers/models';
import { STATUS } from '../helpers/constants';

const applicationPolicyStatusSchema = schemaFactory({
  policyId: { type: Types.ObjectId, ref: 'ApplicationPolicy', required: true, unique: true },
  policyReport: [
    {
      _id: false,
      rportId: { type: String, required: true },
      status: { type: String, required: true, enum: STATUS },
      policies: [
        {
          name: { type: String, required: true },
          vendor: { type: String, required: false },
          versions: [{ type: String }],
          mustBePresent: { type: Boolean, required: true },
          automaticUninstall: { type: Boolean, default: false },
          compliant: { type: Boolean, required: false },
          _id: { type: String, required: true },
        },
      ],
      rules: {
        total: { type: Number, required: true },
        notPassed: { type: Number, required: true },
        passed: { type: Number, required: true },
      },
      compliancePercentage: { type: Number, required: true },
      lastVerifiedAt: { type: Date, required: true },
      deleted: { type: Boolean, default: false },
      compliant: { type: Boolean, required: false },
    },
  ],
  rportIds: [{ type: String }],
  status: {
    type: String,
    enum: [STATUS.COMPLIANT, STATUS.NON_COMPLIANT],
    required: true,
  },
});

applicationPolicyStatusSchema.index({ status: 1 });

export type ApplicationPolicyStatusDocument =
  | (ApplicationPolicyStatusModel & Document & { _id: Types.ObjectId })
  | null;
export const ApplicationPolicyStatus = modelFactory<ApplicationPolicyStatusModel>(
  'ApplicationPolicyStatus',
  applicationPolicyStatusSchema
);
