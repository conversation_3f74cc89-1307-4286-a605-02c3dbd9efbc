import { isEmail } from 'validator';
import { Document, Schema, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '../../../utils/model-factory';

import {
  ApplicationPolicyModel,
  ApplicationPolicy as ApplicationPolicyType,
} from '../helpers/models';

const policySchema = new Schema({
  name: { type: String, required: true },
  vendor: { type: String, required: false },
  versions: { type: [String], required: false },
  mustBePresent: { type: Boolean, required: true },
  automaticUninstall: { type: Boolean, required: false, default: false },
});

const applicationPolicySchema = schemaFactory({
  name: { type: String, required: true },
  group: { type: Types.ObjectId, ref: 'RportGroup', required: true },
  periodicity: { type: Number, required: true, min: 1439, max: 525601 },
  policies: {
    type: [policySchema],
    required: true,
    validate: {
      validator: (policies: ApplicationPolicyType[]) => policies.length >= 1,
      message: 'At least one policy must be provided.',
    },
  },
  notifications: {
    enabled: { type: Boolean, required: true, default: true },
    emails: {
      type: Array,
      required: true,
      validate: {
        validator: (emails: string[]) => emails.every((email) => isEmail(email)),
        message: 'The notification emails contents an invalid email.',
      },
    },
  },
  createdBy: { type: Types.ObjectId, ref: 'User', required: true },
  updatedBy: { type: Types.ObjectId, ref: 'User', required: true },
  lastCheck: { type: Date, required: false },
  nextCheck: { type: Date, required: false },
  lastNotify: { type: Date, required: false },
  nextNotify: { type: Date, required: false },
  groupDeleted: { type: Boolean, required: true, default: false },
});

applicationPolicySchema.index({ name: 1 });
applicationPolicySchema.index({ group: 1 });
applicationPolicySchema.index({ createdBy: 1 });
applicationPolicySchema.index({ updatedBy: 1 });

export type ApplicationPolicyDocument =
  | (ApplicationPolicyModel & Document & { _id: Types.ObjectId })
  | null;
export const ApplicationPolicy = modelFactory<ApplicationPolicyModel>(
  'ApplicationPolicy',
  applicationPolicySchema
);
