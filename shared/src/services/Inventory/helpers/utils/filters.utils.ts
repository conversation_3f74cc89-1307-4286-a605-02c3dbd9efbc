import { isEmpty } from 'lodash';

import { flatOperators, operatorsMap, typesMap } from '@shared/models';

import {
  buildMongoQueryFromFilter,
  parseQueryString,
  splitExtraFilters,
} from '@shared/helpers/classes/schema-utils.class';

import { getValidHostFields } from '@shared/services/Rport/helpers/functions';

import { COLUMNS } from '../constants';

const parseFilter = (filter: string) => {
  const [field, operator, ...valueParts] = filter.split(':');
  const value = valueParts.join(':');

  if (!field || !operator || !value || !flatOperators.includes(operator.toUpperCase()))
    return undefined;

  return { field, operator: operator.toUpperCase(), value };
};

export const buildElasticsearchQuery = (
  filters: string,
  defaultQuery: Record<string, any> = {},
  nested: Record<string, string> = {}
) => {
  const filterArray = filters
    .split('|')
    .map(parseFilter)
    .filter((filter) => !!filter);

  if (filterArray.length === 0) {
    return isEmpty(defaultQuery) ? { match_all: {} } : defaultQuery;
  }

  const mustClauses: Record<string, any>[] = [];
  const mustNotClauses: Record<string, any>[] = [];

  filterArray.forEach((filter) => {
    if (!filter) return;
    let { field } = filter;
    const { operator, value } = filter;

    let query: Record<string, any> | null = null;

    const isKeywordField = field.includes('.keyword');

    // If the field is not a keyword field and the operator is IS or IS_NOT, we need to search in the keyword field
    if (!isKeywordField && ['IS', 'IS_NOT', 'CONTAINS', 'NOT_CONTAINS'].includes(operator)) {
      field = `${field}.keyword`;
    }

    switch (operator) {
      case 'IS':
        query = { term: { [field]: value } };
        break;
      case 'IS_NOT':
        query = { term: { [field]: value } };
        mustNotClauses.push(query);
        return;
      case 'CONTAINS':
        query = { wildcard: { [field]: `*${value}*` } };
        break;
      case 'NOT_CONTAINS':
        query = { wildcard: { [field]: `*${value}*` } };
        mustNotClauses.push(query);
        return;
      case 'GREATER_THAN':
        query = { range: { [field]: { gt: value } } };
        break;
      case 'LESS_THAN':
        query = { range: { [field]: { lt: value } } };
        break;
      case 'AFTER':
        query = { range: { [field]: { gt: new Date(value).toISOString() } } };
        break;
      case 'BEFORE':
        query = { range: { [field]: { lt: new Date(value).toISOString() } } };
        break;
    }

    const fieldWithoutKey = field.replace('.keyword', '');
    if (nested && Object.keys(nested).includes(fieldWithoutKey)) {
      const nestedField = nested[fieldWithoutKey];
      const nestedQuery = {
        nested: {
          path: nestedField,
          query: {
            bool: {
              must: [query],
            },
          },
        },
      };

      mustClauses.push(nestedQuery);
    } else if (query) {
      mustClauses.push(query);
    }
  });

  if (!isEmpty(defaultQuery)) {
    mustClauses.push(defaultQuery);
  }

  return {
    bool: {
      must: mustClauses,
      must_not: mustNotClauses,
    },
  };
};

export const parseFieldKey = (field: string) => {
  const fieldKey = field.replace('Applications.', '');
  return fieldKey.split('.');
};

export const getObjectValue = (
  rawData: Record<string, any>,
  keys: string[]
): string | Date | null | undefined => {
  let data = rawData;

  for (const key of keys) {
    if (data.hasOwnProperty(key)) {
      data = data[key];
    } else {
      return undefined;
    }
  }

  return data as string | Date | null;
};

export const validateSearchFilter = (data: Record<string, any>, value: string) => {
  return COLUMNS.SOFTWARES_BY_HOSTS.some((column) => {
    const fieldKey = parseFieldKey(column);
    const objectValue = getObjectValue(data, fieldKey);

    if (!objectValue) return false;

    return `${objectValue}`.toLowerCase().includes(value.toLowerCase());
  });
};

export const validateContainsFilter = (value: string, expected: string) =>
  `${value}`.toLowerCase().includes(expected.toLowerCase());
export const validateIsFilter = (value: string, expected: string) =>
  `${value}`.toLowerCase() === expected.toLowerCase();

export const generateFieldFilters = (properties: Record<string, string>, fields: string[]) => {
  const fieldFilters: Record<string, any> = {};

  fields.forEach((field) => {
    const elasticType = properties[field] || 'text';
    const filterType = mapElasticTypeToBatuta(elasticType);

    fieldFilters[field] = { type: filterType, operators: operatorsMap[filterType] };
  });

  return fieldFilters;
};

const mapElasticTypeToBatuta = (elasticType: string) => {
  if (elasticType === 'long' || elasticType === 'integer') return typesMap.NUMBER;
  if (elasticType === 'boolean') return typesMap.BOOLEAN;
  if (elasticType === 'date') return typesMap.DATE;
  return typesMap.STRING;
};

export const selectInventoryParser = (columns: string[]) => {
  const columnParsers = {
    [COLUMNS.INVENTORIES_TO_EXPORT.join(',')]: parseInventoryWithApps,
    [COLUMNS.INVENTORIES.join(',')]: parseMinimalInventory,
    [COLUMNS.SOFTWARES_BY_HOSTS.join(',')]: parseInventoryWithApps,
  };

  return columnParsers[columns.join(',')] || (() => ({}));
};

export const parseMinimalInventory = (data: Record<string, any>) => ({
  _id: data._id,
  lastUpdate: data._source.ApplicationsTimestamp,
});

export const parseInventoryWithApps = (data: Record<string, any>) => ({
  _id: data._id,
  lastUpdate: data._source.ApplicationsTimestamp,
  applications: data._source.Applications,
});

export const parseInventoryFilters = async (
  filterString: string
): Promise<{ mongoQuery: { [key: string]: any }; elasticFilters: string }> => {
  // Get valid fields from RportClients
  const validRuleFields = getValidHostFields();

  // Split Filters
  const [mongoFilters, elasticFilters] = splitExtraFilters(filterString, validRuleFields);

  // Generate Query for mongo and elastic
  const filters = parseQueryString(mongoFilters);
  const mongoQuery = buildMongoQueryFromFilter(filters);

  // Return queries
  return { mongoQuery, elasticFilters };
};
