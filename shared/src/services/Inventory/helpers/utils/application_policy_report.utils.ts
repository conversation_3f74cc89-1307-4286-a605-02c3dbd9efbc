import { SearchResponse } from '@elastic/elasticsearch/lib/api/types';

import { Notifier } from '@shared/helpers/classes/notifier.class';
import { SearchHit } from '@elastic/elasticsearch/lib/api/types';
import { t } from '@shared/utils/translation/translator';

import { ApplicationPolicy, HostsReportModel, SoftwareReportModel } from '../models';
import { ApplicationPolicyDocument } from '../../schemas/application_policy.schema';
import { ApplicationPolicyStatusDocument } from '../../schemas/application_policy_status.schema';
import { runElasticSearchQuery } from '../connections/elastic';
import { getOrCreateScanClientTask } from './inventory_scan_policy.utils';
import { addRportClientNames } from './clients.utils';
import { BATUTA_FRONTEND_BASE_URL } from '@shared/constants/env';
import { updateApplicationPolicyStatusByChanges } from './application_policy.utils';
import {
  getHostsByCompliantStatus,
  getHostsByInventoryStatus,
} from './application_policy_status.utils';
import { toPercentage } from '@shared/utils/strings';
import { TLanguage } from '@shared/types/languages.types';
import { UserModel } from '@shared/models';
import { STATUS } from '../constants';

export const pClasses = 'm-0 text-left';

export const getSoftwareString = ({ name, vendor, versions }: ApplicationPolicy): string =>
  `- ${vendor ? `${vendor} - ` : ''}${name}${versions && versions.length > 0 ? ` (${versions.join(', ')})` : ''}`;

export const buildElasticApplicationPolicyQuery = (
  clientIds: string[],
  policies: ApplicationPolicy[]
) => {
  let scriptOneLinerSource: string = `Map policies = new HashMap();`;
  policies.forEach((rule) => {
    scriptOneLinerSource += `policies.put("${rule._id.toString()}", ${!rule.mustBePresent});`;
  });

  scriptOneLinerSource += 'for (app in params._source.Applications) { ';
  policies.forEach((rule) => {
    scriptOneLinerSource += `if (app.Name.toLowerCase().contains('${rule.name.toLowerCase()}')`;

    if (rule.vendor && rule.vendor.length > 0) {
      scriptOneLinerSource += ` && app.Vendor == '${rule.vendor}'`;
    }

    if (rule.versions && rule.versions.length > 0) {
      scriptOneLinerSource += ` && (${rule.versions.map((version) => `app.Version == '${version}'`).join(' || ')})`;
    }

    scriptOneLinerSource += `) {policies.put("${rule._id.toString()}", ${rule.mustBePresent});}`;
  });
  scriptOneLinerSource += '} return policies;';

  return {
    size: clientIds.length,
    _source: false,
    query: {
      bool: {
        must: [
          {
            nested: {
              path: 'Applications',
              query: {
                bool: {
                  must: [
                    {
                      exists: {
                        field: 'Applications',
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
        filter: {
          terms: {
            _id: clientIds,
          },
        },
      },
    },
    script_fields: {
      policies: {
        script: {
          source: scriptOneLinerSource,
        },
      },
    },
  };
};

export const updateNamesToPolicyReport = async (
  policyReport: HostsReportModel[]
): Promise<HostsReportModel[]> => {
  let newPolicyReport: HostsReportModel[] = [];

  if (policyReport.length > 0) {
    newPolicyReport = (await addRportClientNames(policyReport)) as HostsReportModel[];
  }

  return newPolicyReport;
};

export const generatePolicyReport = async (
  hosts: string[],
  hits: SearchHit[],
  policies: ApplicationPolicy[]
): Promise<HostsReportModel[]> => {
  const lastVerifiedAt = new Date();
  const policyReport: HostsReportModel[] = [];

  hosts.forEach((hostId) => {
    const hostResult = hits.find((entry) => entry._id === hostId);

    if (hostResult && hostResult.fields) {
      const hostPolicies: SoftwareReportModel[] = [];
      const fields = hostResult.fields;

      policies.forEach((policy) => {
        const { _id: policyId, automaticUninstall, mustBePresent, name, vendor, versions } = policy;
        const compliant = fields.policies?.[0]?.[policyId.toString()] ?? null;

        hostPolicies.push({
          _id: policyId,
          automaticUninstall,
          mustBePresent,
          name,
          vendor,
          versions,
          compliant,
        });
      });

      const compliant = hostPolicies.every(({ compliant }) => compliant);
      const rules = {
        total: policies.length,
        passed: hostPolicies.filter(({ compliant }) => compliant).length,
        notPassed: hostPolicies.filter(({ compliant }) => !compliant).length,
      };

      const compliancePercentage = parseFloat(toPercentage(rules.passed / rules.total, 2));
      policyReport.push({
        rportId: hostId,
        policies: hostPolicies,
        compliant,
        lastVerifiedAt,
        compliancePercentage,
        rules,
        deleted: false,
        status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
      });
    } else {
      policyReport.push({
        rportId: hostId,
        policies: [],
        compliant: false,
        lastVerifiedAt,
        compliancePercentage: 0,
        rules: {
          total: policies.length,
          passed: 0,
          notPassed: 0,
        },
        deleted: false,
        status: STATUS.WITHOUT_INVENTORY,
      });
    }
  });

  return policyReport;
};

export const notifyReport = (
  user: UserModel,
  policy: ApplicationPolicyDocument,
  policyReport: HostsReportModel[],
  csvContent: Buffer
) => {
  const language = user?.language || 'en';
  const policyName = policy?.name;

  const { hostsWithoutInventory, hostsWithInventory } = getHostsByInventoryStatus(policyReport);
  const { hostsCompliant, hostsNonCompliant } = getHostsByCompliantStatus(hostsWithInventory);

  const withoutInventoryCount = hostsWithoutInventory.length;
  const withInventoryCount = hostsWithInventory.length;
  const compliantCount = hostsCompliant.length;
  const nonCompliantCount = hostsNonCompliant.length;
  const targetedHostsCount = withInventoryCount + withoutInventoryCount;

  Notifier.sendEmail({
    to: user?.email,
    subject: t('email', language, 'applicationPolicySubject', { policyName }),
    templateName: 'applicationPolicy',
    templateValues: {
      greeting: t('email', language, 'applicationPolicyGreeting'),
      title: t('email', language, 'applicationPolicyTitle'),
      subtitle: t('email', language, 'applicationPolicySubtitle'),
      policyName: policyName || t('email', language, 'applicationPolicyPolicyName'),
      buttonURL: `${BATUTA_FRONTEND_BASE_URL}/inventory/control-application-policies/${policy?.id}`,
      buttonText: t('email', language, 'applicationPolicyButtonText'),
      buttonAlt: t('email', language, 'applicationPolicyButtonAlt'),
      targeted_hosts: t('email', language, 'applicationPolicyTargetedHosts'),
      targeted_hosts_value: targetedHostsCount,
      compliant: t('email', language, 'applicationPolicyCompliant'),
      compliant_value: compliantCount,
      non_compliant: t('email', language, 'applicationPolicyNonCompliant'),
      non_compliant_value: nonCompliantCount,
      no_inventory: t('email', language, 'applicationPolicyNoInventory'),
      no_inventory_value: withoutInventoryCount,
      contact_desc: t('email', language, 'applicationPolicyContactDesc'),
      contact: t('email', language, 'applicationPolicyContact'),
      regards: t('email', language, 'applicationPolicyRegards'),
      batuta: t('email', language, 'applicationPolicyBatuta'),
    },
    language,
    attachments: [
      {
        filename: 'report.csv',
        content: csvContent,
        contentType: 'text/csv',
      },
    ],
  });
};

export const generateReport = async (clientIds: string[], policies: ApplicationPolicy[]) => {
  if (policies.length == 0 || clientIds.length == 0) return null;

  const query = buildElasticApplicationPolicyQuery(clientIds, policies);

  // Run query on elastic search
  const response = (await runElasticSearchQuery(
    query._source,
    null,
    clientIds.length,
    query.query,
    [{}],
    {
      script_fields: query.script_fields,
    }
  )) as SearchResponse;

  const hits = response.hits ? response.hits.hits : [];

  // Create policy report from policies, elastic response and clients
  const policyReport = await generatePolicyReport(clientIds, hits, policies);

  // Create task with priority for scan hosts without inventory
  policyReport
    .filter(({ status }) => status === STATUS.WITHOUT_INVENTORY)
    .forEach(({ rportId }) => getOrCreateScanClientTask(rportId, 'SYSTEM'));
  return policyReport;
};

export const getExportedColumns = (language: TLanguage = 'en') => [
  {
    id: '1',
    order: 1,
    field: 'rportId',
    title: t('email', language, 'HOST_ID'),
    format: 'string',
  },
  {
    id: '2',
    order: 2,
    field: 'hostname',
    title: t('email', language, 'HOSTNAME'),
    format: 'string',
  },
  {
    id: '3',
    order: 3,
    field: 'lastVerifiedAt',
    title: t('email', language, 'LAST_CHECK'),
    format: 'datetime',
  },
  {
    id: '4',
    order: 4,
    field: 'status',
    title: t('email', language, 'STATUS'),
    format: 'string',
    translations: {
      NON_COMPLIANT: t('email', language, 'NON_COMPLIANT'),
      COMPLIANT: t('email', language, 'COMPLIANT'),
      WITHOUT_INVENTORY: t('email', language, 'NO_INVENTORY'),
    },
  },
  {
    id: '5',
    order: 5,
    field: 'compliancePercentage',
    title: t('email', language, 'COMPLIANCE_PERCENTAGE'),
    format: 'number',
  },
  {
    id: '6',
    order: 6,
    field: 'rules.passed',
    title: t('email', language, 'RULES_PASSED'),
    format: 'number',
  },
  {
    id: '7',
    order: 7,
    field: 'rules.total',
    title: t('email', language, 'RULES_TOTAL'),
    format: 'number',
  },
  {
    id: '8',
    order: 8,
    field: 'rules.notPassed',
    title: t('email', language, 'RULES_NOT_PASSED'),
    format: 'number',
  },
  {
    id: '9',
    order: 9,
    field: 'name',
    title: t('email', language, 'SOFTWARE_NAME'),
    format: 'string',
  },
  {
    id: '10',
    order: 10,
    field: 'vendor',
    title: t('email', language, 'SOFTWARE_VENDOR'),
    format: 'string',
  },
  {
    id: '11',
    order: 11,
    field: 'versions',
    title: t('email', language, 'SOFTWARE_VERSION(S)'),
    format: 'string',
  },
  {
    id: '12',
    order: 12,
    field: 'mustBePresent',
    title: t('email', language, 'CONDITION'),
    format: 'boolean',
    formatParams: {
      true: t('email', language, 'REQUIRED'),
      false: t('email', language, 'NOT_ALLOWED'),
    },
  },
  {
    id: '13',
    order: 13,
    field: 'compliant',
    title: t('email', language, 'COMPLIES'),
    format: 'boolean',
    formatParams: {
      true: t('email', language, 'COMPLIES'),
      false: t('email', language, 'NOT_COMPLIES'),
    },
  },
];

export const generateReportAndUpdateApplicationPolicyStatusByChanges = async (
  inventoryApplicationPolicy: ApplicationPolicyDocument,
  inventoryApplicationPolicyStatus: ApplicationPolicyStatusDocument,
  newClientIds: string[] = [],
  removedClientIds: string[] = []
): Promise<ApplicationPolicyStatusDocument> => {
  if (!inventoryApplicationPolicy || !inventoryApplicationPolicyStatus) return null;

  const newPolicyReport = await generateReport(newClientIds, inventoryApplicationPolicy.policies);
  const updatedStatus = await updateApplicationPolicyStatusByChanges(
    newPolicyReport,
    inventoryApplicationPolicy,
    inventoryApplicationPolicyStatus,
    newClientIds,
    removedClientIds
  );

  return updatedStatus;
};
