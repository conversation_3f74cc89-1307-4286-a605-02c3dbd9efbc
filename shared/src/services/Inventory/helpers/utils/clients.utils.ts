import { RportClient } from '@shared/services/Rport/schemas';

export const addRportClientNames = async (
  data: Record<string, any>[],
  targetField: string = 'name',
  rportField: string = 'rportId',
  removeNotFound: boolean = false
): Promise<Record<string, any>[]> => {
  const rportIds = data.map((record) => record[rportField]);
  const rportClients = await RportClient.find({ rportId: { $in: rportIds } }).select([
    'name',
    'rportId',
  ]);

  const dataWithNames = data.map((record) => {
    const rportClient = rportClients.find(({ rportId }) => rportId === record[rportField]);
    return { ...record, [targetField]: rportClient ? rportClient.name : 'not_found' };
  });

  if (removeNotFound) {
    return dataWithNames.filter((record) => record[targetField] !== 'not_found');
  }

  return dataWithNames;
};
