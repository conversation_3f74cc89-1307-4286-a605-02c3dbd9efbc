import { Types } from 'mongoose';
import { isEqual } from 'lodash';

import { Service } from '@shared/schemas';
import { RportGroup } from '@shared/services/Rport/schemas';
import {
  ApplicationPolicyDocument,
  ApplicationPolicy as ApplicationPolicySchema,
} from '@shared/services/Inventory/schemas/application_policy.schema';
import { ApplicationPolicyStatus, ApplicationPolicyStatusDocument } from '../../schemas';

import { QueryFilterModel, RelativeType } from '@shared/models';
import { HostsReportModel } from '../models';

import { buildExtraFilters, buildFilterObject } from '@shared/helpers/classes/schema-utils.class';
import { errors } from '@shared/utils/app-errors';

import { getHostIdsFromGroup } from '@shared/services/Rport/helpers/functions';
import { getHostApplicationsFromElastic, uninstallAppFromHosts } from '../functions';

import { applicationPolicySwitch, getAndUpdateNextDate } from './application_policy.utils';
import { generateReport } from './application_policy_report.utils';

import {
  APPLICATION_POLICY_CHECK_PERIOD,
  APPLICATION_POLICY_STATUS_BY_RPORT_ID_CUSTOM_FILTERS,
  APPLICATION_POLICY_STATUS_CUSTOM_FILTERS,
  STATUS,
} from '../constants';
import { Logger } from '@shared/helpers/classes/logger.class';

const customPolicyStatusQueryBuilder = (key: string, relative: RelativeType, value: string) => {
  if (APPLICATION_POLICY_STATUS_CUSTOM_FILTERS.includes(key)) {
    return { [key]: buildFilterObject(relative, value) };
  }

  return {};
};

export const parseApplicationPolicyStatusExtraFilters = (filterObject: QueryFilterModel) => {
  return buildExtraFilters(filterObject, customPolicyStatusQueryBuilder);
};

const customPolicyStatusByRportIdQueryBuilder = (
  key: string,
  relative: RelativeType,
  value: string
) => {
  if (APPLICATION_POLICY_STATUS_BY_RPORT_ID_CUSTOM_FILTERS.includes(key)) {
    return { [key]: buildFilterObject(relative, value) };
  }

  return {};
};

export const parseApplicationPolicyStatusByRportIdExtraFilters = (
  filterObject: QueryFilterModel
) => {
  return buildExtraFilters(filterObject, customPolicyStatusByRportIdQueryBuilder);
};

// Maximum BSON document size is 16MB, we'll use 15MB as safe limit
const MAX_BSON_SIZE = 15 * 1024 * 1024; // 15MB in bytes
const CHUNK_SIZE = 1000; // Process hosts in chunks of 1000

// Helper function to estimate document size
const estimateDocumentSize = (doc: any): number => {
  return Buffer.byteLength(JSON.stringify(doc), 'utf8');
};

// Helper function to process policy reports in chunks
const processReportInChunks = async (
  policyReport: HostsReportModel[],
  clientIds: string[],
  policy: ApplicationPolicyDocument,
  policyStatus: ApplicationPolicyStatusDocument | null,
  compliant: boolean
): Promise<ApplicationPolicyStatusDocument | null> => {
  // If we have an existing status, update it incrementally
  if (policyStatus) {
    for (let i = 0; i < policyReport.length; i += CHUNK_SIZE) {
      const chunk = policyReport.slice(i, i + CHUNK_SIZE);

      // Remove existing reports for these hosts first
      const hostIds = chunk.map((report) => report.rportId);
      await ApplicationPolicyStatus.updateOne(
        { _id: policyStatus._id },
        { $pull: { policyReport: { rportId: { $in: hostIds } } } }
      );

      // Add new reports
      await ApplicationPolicyStatus.updateOne(
        { _id: policyStatus._id },
        {
          $push: { policyReport: { $each: chunk } },
          $set: {
            rportIds: clientIds,
            status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
          },
        }
      );
    }

    return await ApplicationPolicyStatus.findById(policyStatus._id);
  } else {
    // For new documents, check size before creating
    const testDoc = {
      policyId: policy?._id,
      policyReport,
      rportIds: clientIds,
      status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
    };

    const estimatedSize = estimateDocumentSize(testDoc);

    if (estimatedSize > MAX_BSON_SIZE) {
      // Create document with empty policyReport first
      const newPolicyStatus = await ApplicationPolicyStatus.create({
        policyId: policy?._id,
        policyReport: [],
        rportIds: clientIds,
        status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
      });

      // Add policy reports in chunks
      for (let i = 0; i < policyReport.length; i += CHUNK_SIZE) {
        const chunk = policyReport.slice(i, i + CHUNK_SIZE);
        await ApplicationPolicyStatus.updateOne(
          { _id: newPolicyStatus._id },
          { $push: { policyReport: { $each: chunk } } }
        );
      }

      return await ApplicationPolicyStatus.findById(newPolicyStatus._id);
    } else {
      // Document is small enough, create normally
      return await ApplicationPolicyStatus.create(testDoc);
    }
  }
};

export const saveApplicationPolicyStatus = async (
  policyReport: HostsReportModel[],
  clientIds: string[],
  policy: ApplicationPolicyDocument,
  policyStatus: ApplicationPolicyStatusDocument | null
): Promise<ApplicationPolicyStatusDocument | null> => {
  if (!policy || clientIds.length == 0) return Promise.resolve(null);

  const { hostsWithInventory, hostsWithoutInventory } = getHostsByInventoryStatus(policyReport);
  const compliant =
    hostsWithoutInventory.length === 0 && hostsWithInventory.every(({ compliant }) => compliant);

  try {
    return await processReportInChunks(policyReport, clientIds, policy, policyStatus, compliant);
  } catch (error: any) {
    // If we still get BSON size error, log it and try with smaller chunks
    if (error.message?.includes('BSON') || error.message?.includes('buffer')) {
      Logger.error(
        `BSON size error for policy ${policy._id}, attempting with smaller chunks:`,
        error.message
      );

      // Reduce chunk size and try again
      const smallerChunkSize = Math.max(100, Math.floor(CHUNK_SIZE / 2));

      if (policyStatus) {
        // Clear existing reports and rebuild with smaller chunks
        await ApplicationPolicyStatus.updateOne(
          { _id: policyStatus._id },
          {
            $set: {
              policyReport: [],
              rportIds: clientIds,
              status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
            },
          }
        );

        for (let i = 0; i < policyReport.length; i += smallerChunkSize) {
          const chunk = policyReport.slice(i, i + smallerChunkSize);
          await ApplicationPolicyStatus.updateOne(
            { _id: policyStatus._id },
            { $push: { policyReport: { $each: chunk } } }
          );
        }

        return await ApplicationPolicyStatus.findById(policyStatus._id);
      } else {
        // Create new document with smaller chunks
        const newPolicyStatus = await ApplicationPolicyStatus.create({
          policyId: policy._id,
          policyReport: [],
          rportIds: clientIds,
          status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
        });

        for (let i = 0; i < policyReport.length; i += smallerChunkSize) {
          const chunk = policyReport.slice(i, i + smallerChunkSize);
          await ApplicationPolicyStatus.updateOne(
            { _id: newPolicyStatus._id },
            { $push: { policyReport: { $each: chunk } } }
          );
        }

        return await ApplicationPolicyStatus.findById(newPolicyStatus._id);
      }
    }

    // Re-throw other errors
    throw error;
  }
};

export const createTaskForAutomaticUninstall = async (
  policyReport: HostsReportModel[],
  clientIds: string[],
  policy: ApplicationPolicyDocument
) => {
  if (!policy || clientIds.length == 0) return null;

  const softwareToAutomaticUninstall = policy.policies.filter(
    (rule) => !rule.mustBePresent && rule.automaticUninstall
  );

  if (softwareToAutomaticUninstall.length === 0) return null;

  const softwareToUninstallMap: Record<
    string,
    { app: { name: string; vendor?: string }; hosts: string[] }
  > = {};
  for (const app of softwareToAutomaticUninstall) {
    const { _id, name, vendor } = app;
    softwareToUninstallMap[_id.toString()] = { app: { name, vendor }, hosts: [] };
  }

  const hostsWithReports = policyReport.filter(({ status }) => status !== STATUS.WITHOUT_INVENTORY);

  for (const host of hostsWithReports) {
    for (const software of host.policies) {
      const softwareId = software._id.toString();
      const softwareToUninstall = softwareToUninstallMap[softwareId];
      if (!software.mustBePresent && softwareToUninstall) {
        softwareToUninstallMap[softwareId].hosts.push(host.rportId);
      }
    }
  }

  for (const { app, hosts } of Object.values(softwareToUninstallMap)) {
    if (hosts.length > 0) {
      try {
        const hostsWithApps = await getHostApplicationsFromElastic(hosts, app);
        await uninstallAppFromHosts(hostsWithApps, app, {});
      } catch {}
    }
  }
};

export const checkChangesAndUpdateStatus = async (
  policyId: string | Types.ObjectId,
  forceUpdate: boolean = false
) => {
  const policy = await ApplicationPolicySchema.findById(policyId);
  if (!policy) throw errors.not_found('Application Policy');

  if (!policy.enabled) return false;

  const group = await RportGroup.findById(policy.group);
  if (!group || group.deleted || !group.enabled) {
    await applicationPolicySwitch(policyId, null, { groupDeleted: true });
    return false;
  }

  const policyStatus = await ApplicationPolicyStatus.findOne({ policyId });
  const clientIds = await getHostIdsFromGroup(policy.group);

  if (clientIds.length > 0 || forceUpdate) {
    const hasChanges = !isEqual(clientIds.sort(), policyStatus?.rportIds.sort());

    if (forceUpdate || !policyStatus || hasChanges) {
      const policyReport = await generateReport(clientIds, policy.policies);
      await saveApplicationPolicyStatus(policyReport!, clientIds, policy, policyStatus);
      await createTaskForAutomaticUninstall(policyReport!, clientIds, policy);

      await getAndUpdateNextDate(
        APPLICATION_POLICY_CHECK_PERIOD,
        'lastCheck',
        'nextCheck',
        policy._id,
        new Date()
      );
    }
  }
};

export const checkChangesAndUpdateAllStatus = async () => {
  try {
    // Check if the service is enabled
    const serviceEnabled = await Service.findOne({ internalName: 'inventory', enabled: true });
    if (!serviceEnabled) {
      return 'Passive scan service is not enabled';
    }

    const policies = await ApplicationPolicySchema.find({ enabled: true });

    for (const policy of policies) {
      await checkChangesAndUpdateStatus(policy._id);
    }
  } catch (err: any) {
    return `Error checking changes and updating status: ${err.message}`;
  }
};

export const getHostsByInventoryStatus = (policyReport: HostsReportModel[]) => {
  const hostsWithoutInventory = [] as HostsReportModel[];
  const hostsWithInventory = [] as HostsReportModel[];

  for (const report of policyReport) {
    if (report.status === STATUS.WITHOUT_INVENTORY) {
      hostsWithoutInventory.push(report);
    } else {
      hostsWithInventory.push(report);
    }
  }

  return {
    hostsWithoutInventory,
    hostsWithInventory,
  };
};

export const getHostsByCompliantStatus = (policyReport: HostsReportModel[]) => {
  const hostsCompliant = [] as HostsReportModel[];
  const hostsNonCompliant = [] as HostsReportModel[];

  for (const report of policyReport) {
    if (report.status === STATUS.COMPLIANT) {
      hostsCompliant.push(report);
    } else {
      hostsNonCompliant.push(report);
    }
  }

  return {
    hostsCompliant,
    hostsNonCompliant,
  };
};
