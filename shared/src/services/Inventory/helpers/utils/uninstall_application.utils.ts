import { QueuesTask } from '@shared/services/Queues/schemas';

import { BasicApp } from '../models';

import {
  FINISHED,
  IN_PROGRESS,
  PENDING,
  VALIDATING,
} from '@shared/services/Queues/helpers/constants/status';

import { TASK_NAMES } from '@shared/services/Queues/helpers/constants/tasks';
import { RootFilterQuery } from 'mongoose';
import { QueuesTaskModel } from '@shared/services/Queues/helpers/models';
import { TaskParams } from '@shared/services/Queues/helpers/types/task.types';
import { KnownQueuesTaskDocument } from '@shared/services/Queues/schemas/task.schema';

export const isUninstallAppPendingOnHost = async (app: BasicApp, hostId: string) => {
  const fiveMinutesAgo = new Date(new Date().getTime() - 5 * 60 * 1000);

  const query = {
    name: TASK_NAMES.UNINSTALL_SOFTWARE,
    params: {
      rportIds: { $in: [hostId] },
      softwareName: app.name,
      ...(app.vendor && { softwareVendor: app.vendor }),
      ...(app.version && { softwareVersion: app.version }),
    } as Partial<Record<keyof TaskParams<TASK_NAMES.UNINSTALL_SOFTWARE>, any>>,
    $or: [
      { status: { $in: [PENDING, IN_PROGRESS, VALIDATING] } },
      { status: FINISHED, finished: { $gte: fiveMinutesAgo } },
    ],
  } as RootFilterQuery<QueuesTaskModel<TASK_NAMES.UNINSTALL_SOFTWARE>>;

  const task =
    await QueuesTask.findOne<KnownQueuesTaskDocument<TASK_NAMES.UNINSTALL_SOFTWARE>>(query);

  return task !== null;
};
