import { Types } from 'mongoose';
import { isEmpty } from 'lodash';

import TasksFunctions from '@shared/services/Queues/helpers/functions/task.function';

import { errors } from '@shared/utils/app-errors';
import { InventoryScanPolicy } from '@shared/services/Inventory/schemas';
import { CANCELLED, PENDING } from '@shared/services/Queues/helpers/constants/status';
import { QueuesTask } from '@shared/services/Queues/schemas';
import { TASK_NAMES } from '@shared/services/Queues/helpers/constants/tasks';
import { TaskParams } from '@shared/services/Queues/helpers/types/task.types';
import { KnownQueuesTaskDocument } from '@shared/services/Queues/schemas/task.schema';

export const getTask = async (
  inventoryScanPolicyId: string | Types.ObjectId,
  extraFilters: Record<string, any> = {}
) => {
  const task = await QueuesTask.findOne<KnownQueuesTaskDocument<TASK_NAMES.SCAN>>({
    name: TASK_NAMES.SCAN,
    params: {
      scanPolicyId: new Types.ObjectId(inventoryScanPolicyId),
    } as TaskParams<TASK_NAMES.SCAN>,
    ...extraFilters,
  });

  return task;
};

export const getCurrentTask = async (inventoryScanPolicyId: string | Types.ObjectId) => {
  const task = await getTask(inventoryScanPolicyId, { status: PENDING });

  if (!task) throw errors.not_found('task');

  return task;
};

export const getScanClientCurrentTask = async (clientId: string) => {
  const task = await QueuesTask.findOne<KnownQueuesTaskDocument<TASK_NAMES.SCAN_CLIENT>>({
    status: PENDING,
    name: TASK_NAMES.SCAN_CLIENT,
    params: {
      hostId: clientId,
    } as TaskParams<TASK_NAMES.SCAN_CLIENT>,
  });

  return task;
};

export const updateInventoryScanPolicyTask = async (
  inventoryScanPolicyId: string | Types.ObjectId,
  updates: Record<string, any>,
  extraParams: Record<string, any> = {},
  extraFilters: Record<string, any> = {}
) => {
  const task = await QueuesTask.findOneAndUpdate<KnownQueuesTaskDocument<TASK_NAMES.SCAN>>(
    {
      name: TASK_NAMES.SCAN,
      params: {
        scanPolicyId: new Types.ObjectId(inventoryScanPolicyId),
      } as TaskParams<TASK_NAMES.SCAN>,
      ...extraFilters,
    },
    updates,
    extraParams
  );
  if (!task) throw errors.not_found('task');

  return task;
};

export const updateInventoryScanPolicyCurrentTask = async (
  inventoryScanPolicyId: string | Types.ObjectId,
  updates: Record<string, any>,
  extraParams: Record<string, any> = {}
) => {
  const task = await updateInventoryScanPolicyTask(inventoryScanPolicyId, updates, extraParams, {
    status: PENDING,
  });

  return task;
};

export const getInventoryScanPolicy = async (inventoryScanPolicyId: string | Types.ObjectId) => {
  const inventoryScanPolicy = await InventoryScanPolicy.findById(inventoryScanPolicyId);

  if (!inventoryScanPolicy) throw errors.not_found('InventoryScanPolicy');

  return inventoryScanPolicy;
};

export const updateInventoryScanPolicy = async (
  inventoryScanPolicyId: string | Types.ObjectId,
  updates: Record<string, any>,
  extraParams: Record<string, any> = {}
) => {
  const inventoryScanPolicy = await InventoryScanPolicy.findByIdAndUpdate(
    inventoryScanPolicyId,
    updates,
    extraParams
  );

  if (!inventoryScanPolicy) throw errors.not_found('InventoryScanPolicy');

  return inventoryScanPolicy;
};

export const getOrCreateScanClientTask = async (clientId: string, author: string) => {
  const previousTask = await getScanClientCurrentTask(clientId);

  if (previousTask) {
    return previousTask;
  }

  const task = await TasksFunctions.createTask(
    TASK_NAMES.SCAN_CLIENT,
    10,
    { hostId: clientId },
    { author }
  );
  return task;
};

export const scanPolicySwitch = async (
  inventoryScanPolicyId: string | Types.ObjectId,
  updates: Record<string, any> = {}
) => {
  const pipeline: any[] = [{ $set: { enabled: { $not: '$enabled' } } }];
  if (updates && !isEmpty(updates)) pipeline.push({ $set: updates });
  let inventoryScanPolicy = await InventoryScanPolicy.findByIdAndUpdate(
    inventoryScanPolicyId,
    pipeline,
    { new: true }
  );
  if (!inventoryScanPolicy) throw errors.not_found('InventoryScanPolicy');

  const now = new Date();
  if (inventoryScanPolicy.enabled) {
    await updateInventoryScanPolicyTask(
      inventoryScanPolicyId,
      {
        status: PENDING,
        nextCheck: now,
        pending: now,
        params: {
          $set: {
            affectedHosts: [],
          },
        },
        statusDetail: '',
      },
      {},
      { status: CANCELLED }
    );
    inventoryScanPolicy = await InventoryScanPolicy.findByIdAndUpdate(
      inventoryScanPolicyId,
      { nextCheck: now },
      { new: true }
    );
  } else {
    await updateInventoryScanPolicyCurrentTask(inventoryScanPolicyId, {
      cancelled: now,
      status: CANCELLED,
      statusDetail: 'The inventory scan policy is disabled',
    });
  }

  return inventoryScanPolicy;
};
