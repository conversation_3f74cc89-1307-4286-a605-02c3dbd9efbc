export const flattenObject = (
  obj: Record<string, any>,
  parentKey: string = '',
  objToAppend: undefined | Record<string, any> = undefined
) => {
  const result: Record<string, any> = objToAppend || {};

  for (const key in obj) {
    const newKey = parentKey ? `${parentKey}.${key}` : key;
    if (Object.keys(obj[key]).includes('operators')) {
      result[newKey] = obj[key];
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      flattenObject(obj[key], newKey, result);
    } else {
      result[newKey] = obj[key];
    }
  }

  return result;
};
