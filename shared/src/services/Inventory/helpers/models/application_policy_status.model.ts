import { Types } from 'mongoose';

import { SchemaBasicModel } from '@shared/models';
import { STATUS } from '../constants';

export interface SoftwareReportModel {
  name: string;
  vendor?: string;
  versions?: string[];
  mustBePresent: boolean;
  automaticUninstall: boolean;
  compliant: boolean;
  _id: string;
}

export interface HostsReportModel {
  rportId: string;
  name?: string;
  policies: SoftwareReportModel[];
  compliant: boolean;
  lastVerifiedAt: Date;
  compliancePercentage: number;
  deleted: boolean;
  rules: {
    total: number;
    notPassed: number;
    passed: number;
  };
  status: STATUS.COMPLIANT | STATUS.NON_COMPLIANT | STATUS.WITHOUT_INVENTORY;
}

export interface HostsWithoutInventoryModel {
  rportId: string;
  name?: string;
}

export interface ApplicationPolicyStatusModel extends SchemaBasicModel {
  policyId: Types.ObjectId;
  policyReport: HostsReportModel[];
  rportIds: string[];
  status: STATUS.COMPLIANT | STATUS.NON_COMPLIANT;
}
