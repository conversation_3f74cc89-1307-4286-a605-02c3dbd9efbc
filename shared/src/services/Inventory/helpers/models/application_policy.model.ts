import { Types } from 'mongoose';

import { SchemaBasicModel } from '@shared/models';

export interface ApplicationPolicy {
  name: string;
  vendor?: string;
  versions?: string[];
  mustBePresent: boolean;
  automaticUninstall: boolean;
  _id: string;
}

export interface ApplicationPolicyNotifications {
  enabled: boolean;
  emails: string[];
}

export const ApplicationPolicyRequiredFields = ['name', 'mustBePresent'];

export interface ApplicationPolicyModel extends SchemaBasicModel {
  name: string;
  group: Types.ObjectId;
  policies: ApplicationPolicy[];
  notifications: ApplicationPolicyNotifications;
  periodicity: number;
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  groupDeleted: boolean;
  lastCheck?: Date;
  nextCheck?: Date;
  lastNotify?: Date;
  nextNotify?: Date;
}
