import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { PolicyRunHostModel } from '../helpers/models';

const policyRunHostSchema = schemaFactory(
  {
    policy: {
      type: Types.ObjectId,
      ref: 'Policy',
      required: true,
    },
    policy_run: {
      type: Types.ObjectId,
      ref: 'PolicyRun',
      required: true,
    },
    host: {
      type: Types.ObjectId,
      ref: 'RportClient',
      required: true,
    },
    ran_at: { type: Date, required: false },
    status: {
      type: String,
      enum: ['PENDING', 'FAILED', 'SUCCESS', 'ERROR'],
      required: false,
      default: 'PENDING',
    },
  },
  {},
  false
);

policyRunHostSchema.index({ host: 1 });
policyRunHostSchema.index({ policy: 1, host: 1 });
policyRunHostSchema.index({ policy: 1, ran_at: -1 });
policyRunHostSchema.index({ policy: 1, policy_run: 1 });
policyRunHostSchema.index({ policy: 1, host: 1, ran_at: -1 });
policyRunHostSchema.index({ policy_run: 1 });
policyRunHostSchema.index({ policy_run: 1, status: 1 });
policyRunHostSchema.index({ ran_at: 1 });
policyRunHostSchema.index({ ran_at: -1 });
policyRunHostSchema.index({ status: 1 });

policyRunHostSchema.virtual('results', {
  ref: 'ControlResultHost',
  localField: '_id',
  foreignField: 'policyRunHost',
});

export type PolicyRunHostDocument =
  | (PolicyRunHostModel & Document & { _id: Types.ObjectId })
  | null;
export const PolicyRunHost = modelFactory<PolicyRunHostModel>('PolicyRunHost', policyRunHostSchema);
