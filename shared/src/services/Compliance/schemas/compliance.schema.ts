import { Document, model, SchemaTypes, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import prescripts from '@shared/services/Rport/helpers/prescripts';

import { ComplianceModel } from '../helpers/models';

const complianceSchema = schemaFactory({
  platform: { type: String, required: true },
  command: { type: String, required: true },
  parameters: { type: SchemaTypes.Mixed, default: {} },
});

complianceSchema.index({ platform: 1 });
complianceSchema.index({ command: 1 });

complianceSchema.methods.getParsedCommand = function (
  csvUrl: string,
  policyRunId: string,
  os: string
) {
  const fullParameters = { ...this.parameters, csvUrl, policyRunId };
  let formattedCommand = this.command;

  Object.keys(fullParameters).forEach((key) => {
    formattedCommand = formattedCommand.replace(new RegExp(`{{${key}}}`, 'g'), fullParameters[key]);
  });

  if (prescripts[os]) {
    formattedCommand = prescripts[os].addPrescripts(formattedCommand);
  }

  return formattedCommand;
};

export type ComplianceDocument = (ComplianceModel & Document & { _id: Types.ObjectId }) | null;
export const Compliance = model<ComplianceModel>('Compliance', complianceSchema);
