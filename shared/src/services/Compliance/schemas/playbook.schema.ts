import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { PlaybookModel } from '../helpers/models';

const playbookSchema = schemaFactory({
  name: { type: String, required: true, unique: true },
  csvUrl: { type: String, required: true, unique: true },
});

export type PlaybookDocument = PlaybookModel & Document & { _id: Types.ObjectId };
export const Playbook = model<PlaybookModel>('Playbook', playbookSchema);
