import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { PolicyHostLastResultModel } from '../helpers/models';

const policyHostLastResultSchema = schemaFactory(
  {
    policy: {
      type: Types.ObjectId,
      ref: 'Policy',
      required: true,
    },
    host: {
      type: Types.ObjectId,
      ref: 'RportClient',
      required: true,
    },
    policyRun: {
      type: Types.ObjectId,
      ref: 'PolicyRun',
      required: true,
    },
    policyRunHost: {
      type: Types.ObjectId,
      ref: 'PolicyRunHost',
      required: true,
    },
    ran_at: { type: Date, required: false },
    status: {
      type: String,
      enum: ['PENDING', 'FAILED', 'SUCCESS', 'ERROR'],
      required: false,
      default: 'PENDING',
    },
    totalControls: Number,
    passedControls: Number,
    successPercentage: Number,
    createdAt: Date,
    updatedAt: Date,
  },
  {
    timestamps: false,
  },
  false
);

policyHostLastResultSchema.index({ host: 1 });
policyHostLastResultSchema.index({ policy: 1, host: 1 }, { unique: true });
policyHostLastResultSchema.index({ policy: 1, host: 1, status: 1 });
policyHostLastResultSchema.index({ policyRunHost: 1 });
policyHostLastResultSchema.index({ policyRun: 1 });
policyHostLastResultSchema.index({ ran_at: 1 });
policyHostLastResultSchema.index({ ran_at: -1 });
policyHostLastResultSchema.index({ status: 1 });

export type PolicyHostLastResultDocument =
  | (PolicyHostLastResultModel & Document & { _id: Types.ObjectId })
  | null;
export const PolicyHostLastResult = modelFactory<PolicyHostLastResultModel>(
  'PolicyHostLastResult',
  policyHostLastResultSchema
);
