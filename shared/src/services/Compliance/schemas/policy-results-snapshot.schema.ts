import { Document, Schema, Types } from 'mongoose';

import modelFactory from '@shared/utils/model-factory';

import { PolicyResultsSnapshotModel } from '../helpers/models';

const policyResultsSnapshotSchema = new Schema({
  policy: {
    type: Types.ObjectId,
    required: true,
  },
  date: {
    type: Date,
    required: true,
  },
  compliance_status: {
    targeted_hosts: { type: Number, default: 0 },
    not_reached: { type: Number, default: 0 },
    reached: { type: Number, default: 0 },
    compliant: { type: Number, default: 0 },
    not_compliant: { type: Number, default: 0 },
  },
});

policyResultsSnapshotSchema.index({ policy: 1, date: 1 });

export type PolicyResultsSnapshotDocument =
  | (PolicyResultsSnapshotModel & Document & { _id: Types.ObjectId })
  | null;
export const PolicyResultsSnapshot = modelFactory<PolicyResultsSnapshotModel>(
  'PolicyResultsSnapshot',
  policyResultsSnapshotSchema
);
