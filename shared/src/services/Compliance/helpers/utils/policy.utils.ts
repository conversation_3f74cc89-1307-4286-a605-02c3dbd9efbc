import _ from 'lodash';
import { sub, format, isBefore, startOfDay } from 'date-fns';

import { PipelineStage, Types } from 'mongoose';

// Schemas
import { Policy, PolicyDocument, PolicyRunHost } from '../../schemas';
import { RportClient } from '@shared/services/Rport/schemas';

// Utils
import { Time } from '@shared/helpers/classes/times.class';
import { errors } from '@shared/utils/app-errors';
import { delay } from '@shared/utils/delay';

// Functions
import {
  executeCommandOnClientsOrGroups,
  getOnlineClientIds,
} from '@shared/services/Rport/helpers/connections/rport';
import { getHostFromGroup } from '@shared/services/Rport/helpers/functions';
import { isValidToRestart, isValidToRun } from './policyRun.utils';
import { Logger } from '@shared/helpers/classes/logger.class';

export const datesFilter = (dateOption: string) => {
  const now = new Date();
  const options: { [key: string]: number } = {
    last7: 7,
    last14: 14,
    lastmonth: 30,
    last2month: 60,
  };

  const from = new Date(now);
  from.setDate(from.getDate() - options[dateOption]);

  return [from, now];
};

export const isPolicyActive = async (policyId: Types.ObjectId) => {
  // getting current date with timezone
  const current_date = await Time.getDateWithTimeZone();

  // Validating policy
  const policy = await Policy.findById(policyId);
  if (!policy) throw errors.not_found('Policy');

  // Checking if policy should run auto
  if (policy.execution_mode === 'MANUAL') throw errors.not_valid('Policy execution');

  const { life_cycle, schedule } = policy;

  // Check if the current date is within the start and end dates of the lifecycle
  const currentDateTime = current_date.getTime();
  const isWithinLifeCycle =
    currentDateTime >= (await Time.getDateWithTimeZone(life_cycle.start_date)).getTime() &&
    currentDateTime <= (await Time.getDateWithTimeZone(life_cycle.end_date)).getTime();

  if (!isWithinLifeCycle) {
    return false;
  }

  // Check if the current month and weekday match any of the values in the schedule
  const currentMonth = current_date.getMonth();
  const currentWeekday = current_date.getDay();
  const isMonthValid = schedule.month.includes(currentMonth);
  const isWeekdayValid = schedule.week_day.includes(currentWeekday);

  if (!isMonthValid || !isWeekdayValid) {
    return false;
  }

  // Check if the current time falls within the runtime window
  const { start_time, end_time } = schedule.runtime_window;
  const year = current_date.getFullYear(),
    month = current_date.getMonth(),
    day = current_date.getDate();

  const [start_hour, start_minute] = start_time.split(':');
  const [end_hour, end_minute] = end_time.split(':');

  const startTime = new Date(year, month, day, +start_hour, +start_minute, 0, 0);
  const endTime = new Date(year, month, day, +end_hour, +end_minute, 0, 0);

  const currentTime = current_date.getTime() - startTime.getTime();
  const comparingTime = endTime.getTime() - startTime.getTime();

  return currentTime >= 0 && currentTime <= comparingTime;
};

export const getAvailableClients = async (
  policy: PolicyDocument,
  policyRunId: Types.ObjectId,
  limit?: number
) => {
  if (!policy) return [];

  // Calculating expiration for pending status
  const current_date = new Date();
  const halfHourPast = new Date(current_date.getTime() - 30 * 60 * 1000);

  let selectedClients;
  // First, check hosts that currently belongs to the associated group
  const associatedClients = await getHostFromGroup(policy.group);

  const pipeline: PipelineStage[] = [
    {
      $match: {
        policy: policy._id,
        host: { $in: associatedClients.map((client) => client._id) },
        $or: [
          { status: 'PENDING', createdAt: { $gte: halfHourPast } },
          { status: 'SUCCESS' },
          { status: 'FAILED', policy_run: policyRunId },
        ],
      },
    },
    {
      $sort: { updatedAt: -1 },
    },
    {
      $group: {
        _id: {
          host: '$host',
          policy: '$policy',
        },
        createdAt: { $first: '$createdAt' },
        ran_at: { $first: '$ran_at' },
        status: { $first: '$status' },
      },
    },
    {
      $addFields: {
        host: '$_id.host',
        policy: '$_id.policy',
      },
    },
  ];
  const existingResults = await PolicyRunHost.aggregate(pipeline);

  // Non analyzed clients
  const resultHosts = existingResults.map((result) => result.host);
  selectedClients = associatedClients.filter((client) => {
    const clientId = String(client._id);
    return !_.some(resultHosts, (hostId: Types.ObjectId) => String(hostId) === clientId);
  });

  if (policy.operating_mode.update_results == 'PERIODIC') {
    // Besides non analyzed clients, we should include clients with expired results for periodic policies
    const expirationDays = policy.operating_mode.results_expiration || 0; // TODO: should we consider zero days as "always" when undefined?
    const expiredResults = existingResults.filter((result) => {
      // If status is PENDING, then there is no ran_at field, and the result should be considered "NOT EXPIRED" since it happened in the last 30mins
      if (result.status === 'PENDING') return false;
      const expireDate = result.ran_at;
      expireDate.setDate(expireDate.getDate() + expirationDays);
      return +expireDate < +current_date;
    });
    // Non analyzed clients + expired clients
    selectedClients = selectedClients.concat(
      associatedClients.filter(
        (client) => expiredResults.filter((result) => client.id === result.host).length > 0
      )
    );
  }

  let validClients: string[];
  try {
    const onlineIds = await getOnlineClientIds();
    validClients = selectedClients.map((c) => c.rportId).filter((id) => onlineIds.includes(id));
    if (limit) {
      validClients = validClients.slice(0, limit ?? 50);
    }
  } catch (err) {
    throw err;
  }

  return validClients;
};

export const filterValidPolicies = async (
  current_date: Date,
  running: boolean = true
): Promise<PolicyDocument[]> => {
  // Get the policy list
  const policies = await Policy.find({
    execution_mode: 'CRON',
    'life_cycle.start_date': {
      $lte: current_date,
    },
    'life_cycle.end_date': {
      $gte: current_date,
    },
    'schedule.month': current_date.getMonth(),
    'schedule.week_day': current_date.getDay(),
    deleted: false,
    enabled: true,
  });

  if (!policies) return [];

  const validPolicies: PolicyDocument[] = [];

  // Filter using isActivePolicy
  for (const policy of policies) {
    // If the policy exists
    if (!policy) continue;

    // If the policy is active
    let isValid = await isPolicyActive(policy._id);
    if (!isValid) continue;

    if (running) {
      isValid = await isValidToRestart(policy);
    } else {
      isValid = await isValidToRun(policy);
    }

    if (isValid) {
      validPolicies.push(policy);
    }
  }

  return validPolicies;
};

export const runBatchCommand = async (
  ids: string[],
  command: string,
  policyRunId: Types.ObjectId,
  policyId: Types.ObjectId,
  manual: boolean = false
) => {
  const batchesCount = Math.ceil(ids.length / 10);

  // Dividing available clients in groups of 10
  let batches: any[] = [];
  for (let i = 0; i < batchesCount; i++) {
    batches.push(ids.slice(i * 10, (i + 1) * 10));
  }

  // Removing empty arrays
  batches = batches.filter((group) => group.length);

  // Looping through batches
  for (let group of batches) {
    // validating group if the process is manual
    if (manual) {
      try {
        const onlineIds = await getOnlineClientIds();
        group = group.filter((id: string) => onlineIds.includes(id));
      } catch (err) {
        Logger.warning(`Error getting online clients: ${err}`);
        continue;
      }
    }

    // Send the command to the active clients
    try {
      await executeCommandOnClientsOrGroups(
        group,
        [],
        command,
        60,
        'powershell',
        'C:\\Windows\\Temp'
      );
    } catch (error) {
      Logger.warning(`Error sending command to clients: ${error}`);
      continue;
    }

    // Create the policy run hosts
    await Promise.all(
      group.map(async (clientId: string) => {
        const host = await RportClient.findOne({ rportId: clientId, deleted: false });
        if (!host) return;
        // Check if the host already has a result for that policy run
        // this will create it only if it doesn't exist
        // if it exists, it will NOT update it
        await PolicyRunHost.findOneAndUpdate(
          {
            policy: policyId,
            policy_run: policyRunId,
            host: host._id,
            status: 'PENDING',
          },
          {
            $setOnInsert: {
              policy: policyId,
              policy_run: policyRunId,
              host: host._id,
              status: 'PENDING',
            },
          },
          { upsert: true }
        );
      })
    );

    await delay(500);
  }
};

export const getHistoryPeriods = (startDate?: Date): string[] => {
  const currentDate = new Date();
  const periodDates: string[] = [];
  const minDate = startDate ? startOfDay(new Date(startDate)) : undefined;

  for (let i = 0; i <= 90; i += 7) {
    const newDate = sub(currentDate, { days: i });
    if (minDate && isBefore(newDate, minDate)) break;
    const dateString = format(newDate, 'yyyy-MM-dd');
    periodDates.unshift(dateString);
  }

  return periodDates;
};

export const getNextRunAt = async (policy: PolicyDocument) => {
  if (policy.operating_mode.update_results === 'PERIODIC') {
    const end_date = await Time.getDateWithTimeZone(policy.life_cycle.end_date);
    const months = policy.schedule.month;
    const days = policy.schedule.week_day;
    const startTime = policy.schedule.runtime_window.start_time;

    let nextRun = new Date();

    if (end_date && +nextRun > +end_date) return null;

    const currentHour = nextRun.getHours();
    const currentMinutes = nextRun.getMinutes();
    const currentTime = `${currentHour < 10 ? '0' : ''}${currentHour}:${
      currentMinutes < 10 ? '0' : ''
    }${currentMinutes}`;

    let currentDay = nextRun.getDay();

    let day = days.find((d) => (startTime > currentTime ? d >= currentDay : d > currentDay));

    if (day === undefined) {
      day = days.find((d) => d < currentDay);
    }

    if (day === undefined) return null;

    day = nextRun.getDate() + ((7 + day - currentDay) % 7);

    const currentMonth = nextRun.getMonth();

    if (!months.includes(currentMonth)) {
      let month = months.find((m) => m >= currentMonth);

      if (month === undefined) {
        month = months.find((m) => m < currentMonth);
      }

      if (month === undefined) return null;

      nextRun.setDate(1);
      nextRun.setMonth(month);

      currentDay = nextRun.getDay();

      day = days.find((d) => d >= currentDay);

      if (day === undefined) {
        day = days.find((d) => d < currentDay);
      }

      if (day === undefined) return null;

      day = nextRun.getDate() + ((7 + day - currentDay) % 7);
    }

    const [startHours, startMinutes] = startTime.split(':');

    nextRun = new Date(
      nextRun.getFullYear(),
      nextRun.getMonth(),
      day,
      +startHours,
      +startMinutes,
      0,
      0
    );

    if (end_date && +nextRun > +end_date) return null;

    return Time.getTenantDateInUTC(nextRun);
  }

  return null;
};
