import { Time } from '@shared/helpers/classes/times.class';

import { PolicyDocument, PolicyRun, PolicyRunDocument } from '../../schemas';

export const calculatePolicyRunEndTime = async (
  policyRun: PolicyRunDocument,
  policy: PolicyDocument
) => {
  if (!policy || !policyRun) return await Time.getDateWithTimeZone();
  // Extract the date part from the started_at field
  const startedAtDate = await Time.getDateWithTimeZone(policyRun.started_at);

  // Extract the hour and minute parts from the end_time field
  const [endHour, endMinute] = policy.schedule.runtime_window.end_time.split(':');

  // Combine the date, hour and minute parts to create the end time
  const endDateTime = new Date(
    startedAtDate.getFullYear(),
    startedAtDate.getMonth(),
    startedAtDate.getDate(),
    +endHour,
    +endMinute,
    0,
    0
  );

  return endDateTime;
};

export const getPolicyRun = async (
  policy: PolicyDocument,
  mode: string = 'MANUAL'
): Promise<PolicyRunDocument | undefined> => {
  if (!policy) return undefined;

  const latestPolicyRun = await PolicyRun.findOne({
    policy: policy._id,
    running: true,
    mode: 'AUTO',
  }).sort({
    started_at: -1,
  });

  // If the policy doesn't have any policy run create a new one
  if (!latestPolicyRun) {
    return await PolicyRun.create({ policy: policy, started_at: new Date(), mode });
  }

  // Validating if the policy run corresponds to the current period
  const endDateTime = await calculatePolicyRunEndTime(latestPolicyRun, policy);
  const currentDateTime = await Time.getDateWithTimeZone();

  // Check if the policy run is running beyond the allowed time window
  if (currentDateTime.getTime() > endDateTime.getTime()) {
    return await PolicyRun.create({ policy: policy, started_at: new Date(), mode });
  } else {
    // If the current period is ok we return that object
    return latestPolicyRun;
  }
};

export const isValidToRun = async (policy: PolicyDocument): Promise<boolean> => {
  if (!policy) return false;

  // Get the latest run for the policy
  const latestPolicyRun = await PolicyRun.findOne({ policy: policy._id, mode: 'AUTO' }).sort({
    started_at: -1,
  });

  // If the latest policy run exists and is running, it's not valid to run
  if (latestPolicyRun && latestPolicyRun.running) {
    return false;
  } else {
    // Otherwise, it's valid to run
    return true;
  }
};

export const isValidToRestart = async (policy: PolicyDocument): Promise<boolean> => {
  if (!policy) return false;

  // Get the latest run for the policy
  const latestPolicyRun = await PolicyRun.findOne({ policy: policy._id, mode: 'AUTO' }).sort({
    started_at: -1,
  });

  // If the latest policy run exists and is not running, it's not valid to run
  if (latestPolicyRun && latestPolicyRun.running) {
    return true;
  } else {
    // Otherwise, it's valid to run
    return false;
  }
};
