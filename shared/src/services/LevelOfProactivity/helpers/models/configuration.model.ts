import { SearchHit } from '@elastic/elasticsearch/lib/api/types';

export type PlatformRelevanceConfiguration = {
  relevance: 'high' | 'medium' | 'low';
  weight: number;
};

export type PlatformCategory = {
  name: string;
  weight: number;
  relevance: 'high' | 'medium' | 'low';
  coverage: boolean;
};

export type PlatformConfiguration = {
  categories: PlatformCategory[];
  relevances: PlatformRelevanceConfiguration[];
};

export type ComplianceConfiguration = {
  host: string;
  percentage: number;
  success: number;
  total: number;
};

export type OSUpdatesConfiguration = {
  securityUpdatesScore: number;
  updatesScore: number;
};

export const LOP_AREAS = ['compliance', 'platform', 'osUpdates'] as const;

export type VALID_LOP_AREA = (typeof LOP_AREAS)[number];

export type AreaConfiguration<Area extends VALID_LOP_AREA> = Area extends 'compliance'
  ? ComplianceConfiguration
  : Area extends 'platform'
    ? PlatformConfiguration
    : Area extends 'osUpdates'
      ? OSUpdatesConfiguration
      : undefined;

export type AreaContext<Area extends VALID_LOP_AREA> = Area extends 'compliance'
  ? ComplianceConfiguration[]
  : Area extends 'platform'
    ? SearchHit[]
    : undefined;
