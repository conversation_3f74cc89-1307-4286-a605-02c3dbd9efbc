import { AreaConfiguration, AreaContext, LevelOfProactivityScore } from '../models/';

import { RportClientDocument } from '@shared/services/Rport/schemas';

import {
  getComplianceProactivityScoreByHost,
  getComplianceContextForProactivity,
} from '../utils/compliance.utils';
import {
  getPlatformContextForProactivity,
  getPlatformProactivityScoreByHost,
} from '../utils/platform.utils';
import { getOSUpdatesProactivityScoreByHost } from '../utils/updates.utils';
import { VALID_LOP_AREA } from '../models/configuration.model';
import { TSupportedOS } from '@shared/services/Rport/helpers/constants/platforms';

export type AreaScoreFunction<Area extends VALID_LOP_AREA> = (
  client: RportClientDocument,
  configuration: AreaConfiguration<Area>,
  context: AreaContext<Area>
) => Promise<LevelOfProactivityScore>;

export const FUNCTION_BY_AREA = {
  platform: getPlatformProactivityScoreByHost as AreaScoreFunction<'platform'>,
  compliance: getComplianceProactivityScoreByHost as AreaScoreFunction<'compliance'>,
  osUpdates: getOSUpdatesProactivityScoreByHost as AreaScoreFunction<'osUpdates'>,
} as const;

type AreaContextFunction<Area extends VALID_LOP_AREA> = (
  clients: RportClientDocument[]
) => Promise<AreaContext<Area>>;

export const CONTEXT_BY_AREA = {
  platform: getPlatformContextForProactivity as AreaContextFunction<'platform'>,
  compliance: getComplianceContextForProactivity as AreaContextFunction<'compliance'>,
  osUpdates: (async () => {}) as AreaContextFunction<'osUpdates'>,
} as const;

export const SUPPORTED_OS_KERNELS_BY_AREA: Record<VALID_LOP_AREA, readonly TSupportedOS[]> = {
  platform: ['windows', 'linux'],
  compliance: ['windows'],
  osUpdates: ['windows', 'linux', 'darwin'],
} as const;
